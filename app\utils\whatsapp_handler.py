from app.config import settings
from app.utils import human_readable_number
from langfuse import observe


@observe(name="Send WhatsApp Message")
def send_whatsapp_message(to, message):
    from twilio.rest import Client

    # Twilio credentials
    client = Client(settings.TWILIO_ACCOUNT_SID, settings.TWILIO_AUTH_TOKEN)

    # Send a WhatsApp message
    message = client.messages.create(
        body=message,
        from_=settings.TWILIO_PHONE_NUMBER,
        to=to,
    )
    print("Message sent:", message.sid)


def send_button_whatsapp_message(to, message_sid):
    from twilio.rest import Client

    # Twilio credentials
    client = Client(settings.TWILIO_ACCOUNT_SID, settings.TWILIO_AUTH_TOKEN)
    message = client.messages.create(
        content_sid=message_sid,
        to=to,
        from_=settings.TWILIO_PHONE_NUMBER,
    )


def format_whatsapp_message(data):
    """
    Converts JSON data into a structured message for WhatsApp.
    """
    teams = {}

    for entry in data.get("result", []):
        if "LW" in entry["positions"] and "RW" in entry["positions"]:
            entry["positions"].remove("LW")
            entry["positions"].remove("RW")
            entry["positions"].append("Winger")
        if not None in entry["asking_salary"]:
            if len(entry["asking_salary"]) > 1:
                entry["asking_salary"] = (
                    human_readable_number(int(min(entry["asking_salary"])))
                    + " - "
                    + human_readable_number(int(max(entry["asking_salary"])))
                )
            else:
                entry["asking_salary"] = human_readable_number(
                    int(min(entry["asking_salary"]))
                )
        else:
            entry["asking_salary"] = "N/A"

        if not None in entry["transfer_fee"]:
            if len(entry["transfer_fee"]) > 1:
                entry["transfer_fee"] = (
                    human_readable_number(int(min(entry["transfer_fee"])))
                    + " - "
                    + human_readable_number(int(max(entry["transfer_fee"])))
                )
            else:
                entry["transfer_fee"] = human_readable_number(
                    int(min(entry["transfer_fee"]))
                )
        else:
            entry["transfer_fee"] = "N/A"

        team_name = entry["team_name"]

        # Initialize the team entry if not present
        if team_name not in teams:
            teams[team_name] = {
                "positions": [],
                "transfer_fee": [],
                "asking_salary": [],
                "foot": [],
                "max_age": [],
                "contact_person": entry.get("contact_person"),  # Add contact person
                "area_name": entry.get(
                    "area_name"
                ),  # Add area name for country display
            }

        # Append values, replacing None with "N/A"
        teams[team_name]["positions"].extend(entry["positions"])
        teams[team_name]["transfer_fee"].append(entry["transfer_fee"])
        teams[team_name]["asking_salary"].append(entry["asking_salary"])
        teams[team_name]["foot"].append(
            entry["foot"] if entry["foot"] is not None else "N/A"
        )
        teams[team_name]["max_age"].append(
            str(entry["max_age"]) if entry["max_age"] is not None else "N/A"
        )

    formatted_messages = []
    for team, details in teams.items():
        # Format team name with country if available
        team_display = team
        if details.get("area_name"):
            team_display = f"{team} ({details['area_name']})"

        message = (
            f"🔹 *Team:* {team_display}\n"
            f"🔹 *Position:* {', '.join(details['positions'])}\n"
            f"🔹 *TF:* {', '.join(set(details['transfer_fee']))}\n"
            f"🔹 *S:* {', '.join(set(details['asking_salary']))}\n"
            f"🔹 *Foot:* {', '.join(set(details['foot']))}\n"
            f"🔹 *Max Age:* {', '.join(set(details['max_age']))}"
        )

        # Add contact person if available
        if details.get("contact_person"):
            message += f"\n🔹 *Contact:* {details['contact_person']}"

        formatted_messages.append(message)

    return "\n\n".join(formatted_messages)
