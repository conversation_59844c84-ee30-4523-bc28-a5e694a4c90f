router:
  primary_provider: "gemini"
  fallback_provider: "openai"
  model: "gemini-2.5-flash-lite-preview-06-17"
  temperature: 0.1
  max_tokens: 10000
  system_prompt: |
    You are a WhatsApp assistant message router. Your job is to analyze each incoming message and choose exactly one handler:

    1. **Player Handler** (route_to_player_handler)  
       - Trigger: message matches regex  
         `/https?:\/\/(?:www\.)?transfermarkt\.com\/[A-Za-z0-9\-\_\/]+/`  
       - Confidence: 1.0  
       - Reasoning: “Found a Transfermarkt URL via regex”

    2. **Team Handler** (route_to_team_handler)
       - Trigger:
         - Contains team name AND position(s) - for team building requests
         - Contains team name AND player profiles/descriptions AND player position - for team analysis
         - Contains football positions (GK, CB, LCB, RCB, WB, 6, 8, 9, C<PERSON>, RW, LW, STRIKER, GOALKEEPER, CENTRAL DEFENDER, RIGHT WINGBACK, CENTRAL MIDFIELDER, WINGER, ATTAC<PERSON><PERSON> MIDFIELDER, etc.) AND team context
         - Contains player requirements/profiles for a specific team. Must contain position and a team name
         - Message is about wanting players for specific positions
         - Contains transfer-related keywords (TRANSFER TARGETS, TRANSFER WINDOW, looking for players, need players). It must contain team name and position/s
         - Contains detailed player specifications or requirements for a team. Must include position
         - Long messages with team names and multiple position descriptions
       - Confidence: 0.1-1.0 (depending on signal strength)
       - Reasoning: “Team name 'Levski' + position 'rw' found”

    3. **Player Update Handler** (route_to_player_update_handler)
       - Trigger:
         - Contains update keywords (update, change, modify, edit, set) AND player name
         - Message is about updating existing player information
         - Contains player name AND update-related context (salary, position, status, etc.)
         - Mentions changing player details, control stage, or assignment
         - Does NOT contain Transfermarkt URLs (those go to player handler)
       - Confidence: 0.7-1.0 (depending on clarity of update intent)
       - Reasoning: "Update keyword 'change' + player name 'John Smith' found"

    4. **Unclassified** (handle_unclassified_message)
       - Trigger: anything else  
       - Provide best-guess `message_type` (e.g. “greeting”, “question”), and two rephrase suggestions:
         - “Try including a Transfermarkt URL”
         - “Specify what position or budget you're looking for”

    ## Examples
    - “Check out his profile: https://www.transfermarkt.com/john-doe/profil/spieler/12345”
      → route_to_player_handler
    - “Levski needs a CMF and a RW, budget 1.2m”
      → route_to_team_handler
    - "Update John Smith's status to in talks and set salary to 120k"
      → route_to_player_update_handler
    - "Change David Rodriguez position to CAM, assigned to Maria Garcia"
      → route_to_player_update_handler
    - “Hey, what's up?”
      → handle_unclassified_message
    - "looking for a striker, 26 years old, 5 million transfer fee"
      → handle_unclassified_message
    - "Rwd Molenbeek is searching for players. 500k salary"
      → handle_unclassified_message
  
handlers:
  player_handler:
    name: "Player Request Handler"
    description: "Handles player requests with Transfermarkt URLs"
    function_name: "route_to_player_handler"
    enabled: true
    keywords: ["transfermarkt", "player", "profile", "goals", "assists"]
    
  team_handler:
    name: "Team Request Handler"
    description: "Handles team requests"
    function_name: "route_to_team_handler"
    enabled: true
    keywords: ["team", "position", "salary", "transfer fee", "tf", "contact", "pay", "need", "looking", "transfer targets", "transfer window", "striker", "goalkeeper", "central defender", "wingback", "central midfielder", "winger", "attacking midfielder"]

  player_update_handler:
    name: "Player Update Handler"
    description: "Handles player record updates and modifications"
    function_name: "route_to_player_update_handler"
    enabled: true
    keywords: ["update", "change", "modify", "edit", "set", "assign", "status", "salary", "position", "stage", "contact", "video", "description", "price"]

  activity_handler:
    name: "Activity Task Handler"
    description: "Handles football activity task creation and management"
    function_name: "route_to_activity_handler"
    enabled: false  # Will be implemented later
    keywords: ["activity", "task", "schedule", "training", "match"]
    
  statistics_handler:
    name: "Statistics Request Handler"
    description: "Handles football statistics and data requests"
    function_name: "route_to_statistics_handler" 
    enabled: false  # Will be implemented later
    keywords: ["stats", "statistics", "data", "performance", "metrics"]
    
  report_handler:
    name: "Team Report Handler"
    description: "Handles team report generation requests"
    function_name: "route_to_report_handler"
    enabled: false  # Will be implemented later
    keywords: ["report", "summary", "analysis", "overview"]

error_handling:
  default_message: "I couldn't understand your request. Please try rephrasing your message or contact support for assistance."
  log_unclassified: true
  max_retries: 2
  
monitoring:
  langfuse_enabled: true
  log_performance: true
  track_handler_usage: true
  log_routing_decisions: true
  
# Function definitions for OpenAI function calling
functions:
  - name: "route_to_player_handler"
    description: "Route message to player request handler for Transfermarkt URLs"
    parameters:
      type: "object"
      properties:
        message:
          type: "string"
          description: "The original user message to process"
        sender:
          type: "string"
          description: "The sender identifier (WhatsApp number)"
        confidence:
          type: "number"
          description: "Confidence score (0-1) for this routing decision"
        reasoning:
          type: "string"
          description: "Brief explanation of why this handler was chosen"
      required: ["message", "sender", "confidence", "reasoning"]

  - name: "route_to_team_handler"
    description: "Route message to team request handler for team building"
    parameters:
      type: "object"
      properties:
        message:
          type: "string"
          description: "The original user message to process"
        sender:
          type: "string"
          description: "The sender identifier (WhatsApp number)"
        confidence:
          type: "number"
          description: "Confidence score (0-1) for this routing decision"
        reasoning:
          type: "string"
          description: "Brief explanation of why this handler was chosen"
      required: ["message", "sender", "confidence", "reasoning"]

  - name: "route_to_player_update_handler"
    description: "Route message to player update handler for modifying existing player records"
    parameters:
      type: "object"
      properties:
        message:
          type: "string"
          description: "The original user message to process"
        sender:
          type: "string"
          description: "The sender identifier (WhatsApp number)"
        confidence:
          type: "number"
          description: "Confidence score (0-1) for this routing decision"
        reasoning:
          type: "string"
          description: "Brief explanation of why this handler was chosen"
      required: ["message", "sender", "confidence", "reasoning"]

  - name: "handle_unclassified_message"
    description: "Handle messages that don't fit into any specific category"
    parameters:
      type: "object"
      properties:
        message:
          type: "string"
          description: "The original user message that couldn't be classified"
        sender:
          type: "string"
          description: "The sender identifier (WhatsApp number)"
        message_type:
          type: "string"
          description: "Best guess at what type of message this might be"
        suggestions:
          type: "array"
          items:
            type: "string"
          description: "Suggestions for how the user could rephrase their message"
      required: ["message", "sender", "message_type", "suggestions"]
