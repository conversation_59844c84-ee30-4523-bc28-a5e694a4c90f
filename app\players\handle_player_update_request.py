import re
import json
import time
import logging
from langfuse import observe
from app.db import insert_whatsapp_request
from app.monitoring.whatsapp_tracer import trace_assistant, trace_response, trace_error
from app.utils.ai_client import get_ai_client
from app.utils import get_absolute_path
from app.utils.whatsapp_handler import (
    send_whatsapp_message,
    send_button_whatsapp_message,
)
from app.utils.secure_http_client import get_secure_http_client

logger = logging.getLogger(__name__)

# Export functions for use in main webhook
__all__ = [
    "process_player_update_request",
    "handle_player_update_confirmation",
    "handle_player_selection",
    "handle_contact_selection",
]


def _load_player_update_instructions():
    """Load player update assistant instructions from file."""
    try:
        instructions_path = get_absolute_path(
            "app/players/player_update_instructions.txt"
        )
        with open(instructions_path, "r", encoding="utf-8") as f:
            return f.read().strip()
    except FileNotFoundError:
        raise FileNotFoundError("player_update_instructions.txt not found")


@observe(name="Player Update Handler", as_type="generation")
async def process_player_update_request(
    sender: str, message: str, client, user_sessions: dict, trace_id: str = None
):
    """Handle player update request processing using AI extraction"""
    try:
        # Log the request
        try:
            insert_whatsapp_request(sender, message)
        except Exception as e:
            logger.warning(f"Error saving the player update log: {e}")

        phone_number = sender.split(":")[1]

        # ✅ Step 1: AI Processing (Extract update fields)
        from langfuse import get_client

        langfuse = get_client()
        ai_client = get_ai_client()

        with langfuse.start_as_current_generation(
            name="AI Processing - Player Update",
            model="gemini-2.5-flash-lite-preview-06-17",
            input={"message": message, "sender": sender},
            metadata={"component": "player_update_handler", "step": "ai_processing"},
        ) as ai_span:
            assistant_start_time = time.perf_counter()

            player_update_instructions = _load_player_update_instructions()

            try:
                ai_response = await ai_client.chat_completion(
                    messages=[
                        {"role": "system", "content": player_update_instructions},
                        {"role": "user", "content": message},
                    ],
                    model="gemini-2.5-flash-lite-preview-06-17",
                    temperature=0.1,
                    max_tokens=1500,
                    response_format={"type": "json_object"},
                )

                assistant_duration = time.perf_counter() - assistant_start_time
                logger.info(
                    f"🕒 AI processing took {assistant_duration:.4f} sec using {ai_response.provider.value}"
                )

                # Parse AI response
                if ai_response.content:
                    try:
                        assistant_response = json.loads(ai_response.content)
                    except json.JSONDecodeError as e:
                        logger.error(f"Failed to parse AI response as JSON: {e}")
                        assistant_response = None
                else:
                    assistant_response = None

                # Update AI span with results
                ai_span.update(
                    output={
                        "assistant_response": assistant_response,
                        "processing_time": assistant_duration,
                        "provider": ai_response.provider.value,
                    }
                )

            except Exception as e:
                logger.error(f"AI processing error: {str(e)}")
                assistant_response = None
                ai_span.update(output={"error": str(e)})

        # Validate AI response
        if not assistant_response or not assistant_response.get("player_name"):
            error_msg = "⚠ Could not identify the player to update. Please specify the player's name clearly in your message."
            send_whatsapp_message(sender, error_msg)

            if trace_id:
                await trace_response(trace_id, sender, error_msg, False)
            return

        # ✅ Step 2: Player and Contact Lookup
        with langfuse.start_as_current_span(
            name="Player and Contact Lookup",
            input={
                "player_name": assistant_response.get("player_name"),
                "assigned_contact": assistant_response.get("assigned_to_record"),
            },
            metadata={"component": "player_update_handler", "step": "lookup"},
        ) as lookup_span:
            lookup_start = time.perf_counter()

            # Check player exists using secure client
            player_name = assistant_response.get("player_name")
            client = get_secure_http_client()

            player_response = client.get(
                "/player_records/whatsapp/check_player",
                params={"phone_number": phone_number, "player_name": player_name},
                sender=sender,
                timeout=10,
            )

            if not player_response.success:
                error_msg = f"⚠ Could not find player '{player_name}' in your records. Please check the player name and try again."
                # Only send message if not already handled by secure client (auth/rate limit errors)
                if player_response.status_code not in [401, 403, 429]:
                    send_whatsapp_message(sender, error_msg)

                lookup_span.update(
                    output={
                        "error": "Player not found",
                        "status_code": player_response.status_code,
                    }
                )
                if trace_id:
                    await trace_response(trace_id, sender, error_msg, False)
                return

            player_data = player_response.data

            if not player_data.get("player_id"):
                error_msg = f"⚠ Could not retrieve player ID for '{player_name}'. Please try again."
                send_whatsapp_message(sender, error_msg)

                lookup_span.update(output={"error": "No player ID returned"})
                if trace_id:
                    await trace_response(trace_id, sender, error_msg, False)
                return

            # Check contact if assigned_to_record is provided
            contact_id = None
            assigned_contact = assistant_response.get("assigned_to_record")
            if assigned_contact:
                contact_params = {
                    "phone_number": phone_number,
                    "contact_name": assigned_contact,
                }

                contact_response = client.get(
                    "/contacts/whatsapp/check_contact",
                    params=contact_params,
                    sender=sender,
                    timeout=10,
                )

                if contact_response.success:
                    contact_id = contact_response.data.get("contact_id")
                    if not contact_id:
                        logger.warning(
                            f"Contact '{assigned_contact}' found but no contact_id returned"
                        )
                else:
                    # Only log if not auth/rate limit error (those are handled by client)
                    if contact_response.status_code not in [401, 403, 429]:
                        logger.warning(
                            f"Contact '{assigned_contact}' not found (status: {contact_response.status_code})"
                        )
                    # Continue without contact - this is not a blocking error

            lookup_duration = time.perf_counter() - lookup_start
            logger.info(f"🔍 Lookup completed in {lookup_duration:.4f} sec")

            lookup_span.update(
                output={
                    "player_id": player_data.get("player_id"),
                    "contact_id": contact_id,
                    "lookup_time": lookup_duration,
                }
            )

        # ✅ Step 3: Store Update Data and Send Confirmation
        await store_update_data_and_confirm(
            sender,
            assistant_response,
            player_data,
            contact_data if assigned_contact else None,
            user_sessions,
            trace_id,
        )

    except Exception as e:
        error_msg = "⚠ Error processing player update request. Please try again."
        send_whatsapp_message(sender, error_msg)
        logger.error(f"Player update processing error: {str(e)}")

        if trace_id:
            await trace_error(trace_id, sender, str(e))


async def handle_player_update(
    sender: str,
    update_data: dict,
    player_id: str,
    player_id_numeric: int,
    contact_id: str,
    user_sessions: dict,
    trace_id: str = None,
):
    """Handle the actual player update API call"""

    phone_number = sender.split(":")[1]

    # Build update payload (id will be sent as query parameter)
    update_payload = {}

    # Include playerId if provided
    if player_id_numeric is not None:
        update_payload["playerId"] = player_id_numeric
        logger.info(f"Including playerId: {player_id_numeric}")
    else:
        logger.warning(f"playerId is None, not including in payload")

    # Add fields that have values
    field_mapping = {
        "control_stage": "control_stage",
        "position": "position",
        "club_asking_price": "club_asking_price",
        "current_gross_salary": "current_gross_salary",
        "expected_net_salary": "expected_net_salary",
        "video_link": "video_link",
        "description": "comment",
    }

    for ai_field, api_field in field_mapping.items():
        if update_data.get(ai_field) is not None:
            update_payload[api_field] = update_data[ai_field]

    # Handle assigned contact - needs to be an array of contact IDs
    if contact_id:
        update_payload["assigned_to_record"] = [contact_id]

    # Make secure update API call
    client = get_secure_http_client()
    update_params = {"phone_number": phone_number, "id": player_id}

    api_response = client.put(
        "/player_records/whatsapp/update",
        params=update_params,
        json_data=update_payload,
        sender=sender,
        timeout=15,
    )

    # Handle response
    if api_response.success:
        success_msg = f"✅ Player updated successfully!"

        if api_response.data.get("tokens_left") is not None:
            success_msg += f" Tokens left: {api_response.data['tokens_left']}"

        send_whatsapp_message(sender, success_msg)

        if trace_id:
            await trace_response(trace_id, sender, success_msg, True)

    else:
        # Handle specific business logic errors (not auth/rate limit - those are handled by client)
        if api_response.status_code == 402:
            tokens_left = api_response.data.get("tokens_left", 0)
            error_msg = f"⚠ Not enough tokens to update player record! Tokens left: {tokens_left}"
        elif api_response.status_code == 404:
            error_msg = f"⚠ Player record not found. Please check the player name."
        elif api_response.status_code not in [401, 403, 429]:
            # Don't send additional messages for auth/rate limit errors (handled by client)
            error_detail = api_response.data.get(
                "detail", f"HTTP {api_response.status_code}"
            )
            error_msg = f"⚠ Failed to update player record: {error_detail}"
        else:
            error_msg = None  # Auth/rate limit errors already handled by client

        if error_msg:
            send_whatsapp_message(sender, error_msg)

            if trace_id:
                await trace_response(trace_id, sender, error_msg, False)


async def store_update_data_and_confirm(
    sender: str,
    update_data: dict,
    player_data: dict,
    contact_data: dict,
    user_sessions: dict,
    trace_id: str = None,
):
    """Store update data in session and send confirmation template"""

    # Debug logging for API response
    logger.info(f"Player API response: {player_data}")

    # Store all data in user session
    user_sessions[sender] = {
        "type": "player_update",
        "status": "awaiting_confirmation",
        "update_data": update_data,
        "selected_player": {
            "player_id": player_data.get("player_id"),
            "playerId": player_data.get("playerId"),
            "fullName": player_data.get("fullName"),
            "similarity_score": player_data.get("similarity_score"),
            "top_matches": player_data.get("top_matches", []),
        },
        "selected_contact": (
            {
                "contact_id": contact_data.get("contact_id") if contact_data else None,
                "contactId": contact_data.get("contactId") if contact_data else None,
                "fullName": contact_data.get("fullName") if contact_data else None,
                "similarity_score": (
                    contact_data.get("similarity_score") if contact_data else None
                ),
                "top_matches": (
                    contact_data.get("top_matches", []) if contact_data else []
                ),
            }
            if contact_data
            else None
        ),
    }

    # Send formatted confirmation message
    await send_player_update_confirmation(sender, user_sessions[sender])


async def send_player_update_confirmation(sender: str, session_data: dict):
    """Send formatted confirmation message and template"""

    update_data = session_data["update_data"]
    selected_player = session_data["selected_player"]
    selected_contact = session_data.get("selected_contact")

    # Build confirmation message
    confirmation_msg = "📝 *Player Update Confirmation:*\n\n"
    confirmation_msg += f"👤 *Player:* {selected_player['fullName']}\n"

    if update_data.get("control_stage"):
        confirmation_msg += f"🎮 *Control Stage:* {update_data['control_stage']}\n"

    if update_data.get("position"):
        confirmation_msg += f"⚽ *Position:* {update_data['position'].upper()}\n"

    if update_data.get("club_asking_price"):
        confirmation_msg += (
            f"💰 *Club Asking Price:* €{update_data['club_asking_price']:,}\n"
        )

    if update_data.get("current_gross_salary"):
        confirmation_msg += (
            f"💵 *Current Salary:* €{update_data['current_gross_salary']:,}\n"
        )

    if update_data.get("expected_net_salary"):
        confirmation_msg += (
            f"💸 *Expected Salary:* €{update_data['expected_net_salary']:,}\n"
        )

    if update_data.get("video_link"):
        confirmation_msg += f"🎥 *Video Link:* {update_data['video_link']}\n"

    if selected_contact and selected_contact.get("fullName"):
        confirmation_msg += f"👥 *Assigned Contact:* {selected_contact['fullName']}\n"

    if update_data.get("description"):
        confirmation_msg += f"\n📄 *Comment:* {update_data['description']}"

    # Send confirmation message
    send_whatsapp_message(sender, confirmation_msg)

    # Send template with options
    template_id = "HX8049e8c3f7a324ebb6d70bd745f3b798"
    send_button_whatsapp_message(sender, template_id)


async def handle_player_update_confirmation(
    sender: str, message: str, user_sessions: dict
):
    """Handle template button responses for player update confirmation"""

    session = user_sessions.get(sender, {})
    if (
        session.get("type") != "player_update"
        or session.get("status") != "awaiting_confirmation"
    ):
        send_whatsapp_message(sender, "⚠ No pending player update found.")
        return

    # Use exact button payload matching for template buttons
    if message == "proceed_update_player":
        # Proceed with the update
        await execute_player_update(sender, session, user_sessions)

    elif message == "change_player":
        # Show player selection list
        await show_player_selection(sender, session, user_sessions)

    elif message == "change_contact":
        # Show contact selection list or handle no contact case
        await show_contact_selection(sender, session, user_sessions)

    else:
        # Invalid response - log the actual message for debugging
        logger.warning(
            f"Unrecognized player update confirmation response from {sender}: '{message}'"
        )
        send_whatsapp_message(
            sender,
            f"⚠ Please use the template buttons to respond. (Received: '{message}')",
        )


async def execute_player_update(sender: str, session_data: dict, user_sessions: dict):
    """Execute the actual player update"""

    update_data = session_data["update_data"]
    selected_player = session_data["selected_player"]
    selected_contact = session_data.get("selected_contact")

    player_id = selected_player["player_id"]
    player_id_numeric = selected_player["playerId"]  # Get playerId from session
    contact_id = selected_contact.get("contact_id") if selected_contact else None

    # Clear session
    user_sessions[sender] = {}

    # Call the original update function with playerId
    await handle_player_update(
        sender, update_data, player_id, player_id_numeric, contact_id, user_sessions
    )


async def show_player_selection(sender: str, session_data: dict, user_sessions: dict):
    """Show numbered list of player matches for selection"""

    selected_player = session_data["selected_player"]
    top_matches = selected_player.get("top_matches", [])

    if not top_matches:
        send_whatsapp_message(sender, "⚠ No alternative player matches found.")
        await send_player_update_confirmation(sender, session_data)
        return

    # Update session status
    user_sessions[sender]["status"] = "awaiting_player_selection"

    # Build selection message
    selection_msg = "👤 *Select Player:*\n\n"

    for i, player in enumerate(top_matches[:5], 1):
        selection_msg += f"{i}. {player['fullName']}\n"

    selection_msg += "\nPlease send the number of your choice (1-5):"
    send_whatsapp_message(sender, selection_msg)


async def show_contact_selection(sender: str, session_data: dict, user_sessions: dict):
    """Show numbered list of contact matches for selection"""

    selected_contact = session_data.get("selected_contact")

    if not selected_contact:
        send_whatsapp_message(sender, "⚠ No contact was specified in this update.")
        await send_player_update_confirmation(sender, session_data)
        return

    top_matches = selected_contact.get("top_matches", [])

    if not top_matches:
        send_whatsapp_message(sender, "⚠ No alternative contact matches found.")
        await send_player_update_confirmation(sender, session_data)
        return

    # Update session status
    user_sessions[sender]["status"] = "awaiting_contact_selection"

    # Build selection message
    selection_msg = "👥 *Select Contact:*\n\n"

    for i, contact in enumerate(top_matches[:5], 1):
        selection_msg += f"{i}. {contact['fullName']}\n"

    selection_msg += "\nPlease send the number of your choice (1-5):"
    send_whatsapp_message(sender, selection_msg)


async def handle_player_selection(sender: str, message: str, user_sessions: dict):
    """Handle numeric player selection"""

    session = user_sessions.get(sender, {})
    if (
        session.get("type") != "player_update"
        or session.get("status") != "awaiting_player_selection"
    ):
        send_whatsapp_message(sender, "⚠ No pending player selection found.")
        return

    try:
        choice = int(message.strip())
        selected_player = session["selected_player"]
        top_matches = selected_player.get("top_matches", [])

        if 1 <= choice <= len(top_matches) and choice <= 5:
            # Update selected player
            new_player = top_matches[choice - 1]
            session["selected_player"] = {
                "player_id": new_player["player_id"],
                "playerId": new_player["playerId"],
                "fullName": new_player["fullName"],
                "similarity_score": new_player["similarity_score"],
                "top_matches": top_matches,
            }

            # Reset status and send confirmation again
            session["status"] = "awaiting_confirmation"
            await send_player_update_confirmation(sender, session)

        else:
            send_whatsapp_message(sender, "Please pick a valid number")
            await show_player_selection(sender, session, user_sessions)

    except ValueError:
        send_whatsapp_message(sender, "Please pick a valid number")
        await show_player_selection(sender, session, user_sessions)


async def handle_contact_selection(sender: str, message: str, user_sessions: dict):
    """Handle numeric contact selection"""

    session = user_sessions.get(sender, {})
    if (
        session.get("type") != "player_update"
        or session.get("status") != "awaiting_contact_selection"
    ):
        send_whatsapp_message(sender, "⚠ No pending contact selection found.")
        return

    try:
        choice = int(message.strip())
        selected_contact = session.get("selected_contact", {})
        top_matches = selected_contact.get("top_matches", [])

        if 1 <= choice <= len(top_matches) and choice <= 5:
            # Update selected contact
            new_contact = top_matches[choice - 1]
            session["selected_contact"] = {
                "contact_id": new_contact["contact_id"],
                "contactId": new_contact.get(
                    "contact_id"
                ),  # Use contact_id for both fields
                "fullName": new_contact["fullName"],
                "similarity_score": new_contact["similarity_score"],
                "top_matches": top_matches,
            }

            # Reset status and send confirmation again
            session["status"] = "awaiting_confirmation"
            await send_player_update_confirmation(sender, session)

        else:
            send_whatsapp_message(sender, "Please pick a valid number")
            await show_contact_selection(sender, session, user_sessions)

    except ValueError:
        send_whatsapp_message(sender, "Please pick a valid number")
        await show_contact_selection(sender, session, user_sessions)
