import logging
import time
from typing import Dict, Callable, Any, Optional
from langfuse import observe
from app.players.handle_player_request import process_player_request
from app.players.handle_player_update_request import process_player_update_request
from app.teams.handle_team_request import handle_team_request
from app.utils.whatsapp_handler import send_whatsapp_message

logger = logging.getLogger(__name__)


class HandlerRegistry:
    """
    Registry for managing and executing WhatsApp message handlers.

    This class maintains a mapping of handler names to their corresponding
    functions and provides a unified interface for executing them.
    """

    def __init__(self):
        """Initialize the handler registry with available handlers."""
        self.handlers: Dict[str, Callable] = {}
        self.handler_metadata: Dict[str, Dict[str, Any]] = {}
        self.performance_metrics: Dict[str, Dict[str, Any]] = {}

        self._register_handlers()
        logger.info(f"HandlerRegistry initialized with {len(self.handlers)} handlers")

    def _register_handlers(self):
        """Register all available handlers with their metadata."""

        # Player handler
        self.handlers["player_handler"] = self._wrap_player_handler
        self.handler_metadata["player_handler"] = {
            "name": "Player Request Handler",
            "description": "Handles player analysis requests with Transfermarkt URLs",
            "requires_client": True,
            "requires_user_sessions": True,
        }

        # Team handler
        self.handlers["team_handler"] = self._wrap_team_handler
        self.handler_metadata["team_handler"] = {
            "name": "Team Request Handler",
            "description": "Handles team requests",
            "requires_client": True,
            "requires_user_sessions": True,
        }

        # Player update handler
        self.handlers["player_update_handler"] = self._wrap_player_update_handler
        self.handler_metadata["player_update_handler"] = {
            "name": "Player Update Handler",
            "description": "Handles player record updates and modifications",
            "requires_client": True,
            "requires_user_sessions": True,
        }

        # Unclassified message handler
        self.handlers["unclassified"] = self._handle_unclassified
        self.handler_metadata["unclassified"] = {
            "name": "Unclassified Message Handler",
            "description": "Handles messages that couldn't be classified",
            "requires_client": False,
            "requires_user_sessions": False,
        }

        # Initialize performance metrics
        for handler_name in self.handlers.keys():
            self.performance_metrics[handler_name] = {
                "total_calls": 0,
                "total_duration": 0.0,
                "average_duration": 0.0,
                "last_called": None,
                "error_count": 0,
            }

    @observe(name="Handler Execution")
    async def execute_handler(
        self,
        handler_name: str,
        function_args: Dict[str, Any],
        client=None,
        user_sessions: Optional[Dict] = None,
        trace_id: str = None,
    ) -> Any:
        """
        Execute the specified handler with the given arguments.

        Args:
            handler_name: Name of the handler to execute
            function_args: Arguments from the router function call
            client: OpenAI client (if required by handler)
            user_sessions: User session data (if required by handler)
            trace_id: Thread ID for monitoring (if available)

        Returns:
            Result from the handler execution
        """
        if handler_name not in self.handlers:
            logger.error(f"Handler '{handler_name}' not found in registry")
            return await self._handle_unknown_handler(
                function_args.get("sender"), function_args.get("message")
            )

        start_time = time.perf_counter()

        try:
            logger.info(f"Executing handler: {handler_name}")

            # Get handler function and metadata
            handler_func = self.handlers[handler_name]
            metadata = self.handler_metadata[handler_name]

            # Prepare arguments based on handler requirements
            kwargs = {"function_args": function_args}

            if metadata.get("requires_client") and client:
                kwargs["client"] = client

            if metadata.get("requires_user_sessions") and user_sessions is not None:
                kwargs["user_sessions"] = user_sessions

            # Add thread_id if available
            if trace_id:
                kwargs["trace_id"] = trace_id

            # Execute the handler
            result = await handler_func(**kwargs)

            # Track performance
            duration = time.perf_counter() - start_time
            self._update_performance_metrics(handler_name, duration, success=True)

            # Track in performance monitor
            try:
                from app.monitoring.performance_monitor import performance_monitor

                performance_monitor.track_handler_execution(
                    handler_name=handler_name,
                    sender=function_args.get("sender", "unknown"),
                    execution_time=duration,
                    success=True,
                )
            except Exception as e:
                logger.warning(f"Failed to track handler execution: {e}")

            logger.info(f"Handler {handler_name} completed in {duration:.4f}s")
            return result

        except Exception as e:
            duration = time.perf_counter() - start_time
            self._update_performance_metrics(handler_name, duration, success=False)

            # Track in performance monitor
            try:
                from app.monitoring.performance_monitor import performance_monitor

                performance_monitor.track_handler_execution(
                    handler_name=handler_name,
                    sender=function_args.get("sender", "unknown"),
                    execution_time=duration,
                    success=False,
                    error_message=str(e),
                )
            except Exception as monitor_error:
                logger.warning(
                    f"Failed to track handler execution error: {monitor_error}"
                )

            logger.error(f"Error executing handler {handler_name}: {str(e)}")
            return await self._handle_execution_error(
                function_args.get("sender"),
                function_args.get("message"),
                handler_name,
                str(e),
            )

    async def _wrap_player_handler(
        self,
        function_args: Dict[str, Any],
        client,
        user_sessions: Dict,
        trace_id: str = None,
    ):
        """Wrapper for the player request handler."""
        # Debug logging to understand the issue
        logger.info(f"Player handler function_args: {function_args}")

        # Robust parameter extraction with fallbacks
        sender = function_args.get("sender")
        message = function_args.get("message")

        if not sender:
            logger.error(f"Missing 'sender' in function_args: {function_args}")
            # Try to extract from trace_id or other sources
            if trace_id and ":" in str(trace_id):
                sender = str(trace_id)
                logger.warning(f"Using trace_id as sender fallback: {sender}")
            else:
                raise ValueError(
                    "Missing required 'sender' parameter in function arguments"
                )

        if not message:
            logger.error(f"Missing 'message' in function_args: {function_args}")
            raise ValueError(
                "Missing required 'message' parameter in function arguments"
            )

        logger.info(f"Processing player request from {sender}")
        return await process_player_request(
            sender, message, client, user_sessions, trace_id
        )

    async def _wrap_team_handler(
        self,
        function_args: Dict[str, Any],
        client,
        user_sessions: Dict,
        trace_id: str = None,
    ):
        """Wrapper for the team request handler."""
        # Debug logging to understand the issue
        logger.info(f"Team handler function_args: {function_args}")

        # Robust parameter extraction with fallbacks
        sender = function_args.get("sender")
        message = function_args.get("message")

        if not sender:
            logger.error(f"Missing 'sender' in function_args: {function_args}")
            # Try to extract from trace_id or other sources
            if trace_id and ":" in str(trace_id):
                sender = str(trace_id)
                logger.warning(f"Using trace_id as sender fallback: {sender}")
            else:
                raise ValueError(
                    "Missing required 'sender' parameter in function arguments"
                )

        if not message:
            logger.error(f"Missing 'message' in function_args: {function_args}")
            raise ValueError(
                "Missing required 'message' parameter in function arguments"
            )

        logger.info(f"Processing team request from {sender}")
        return await handle_team_request(
            sender, message, client, user_sessions, trace_id
        )

    async def _wrap_player_update_handler(
        self,
        function_args: Dict[str, Any],
        client,
        user_sessions: Dict,
        trace_id: str = None,
    ):
        """Wrapper for the player update request handler."""
        # Debug logging to understand the issue
        logger.info(f"Player update handler function_args: {function_args}")

        # Robust parameter extraction with fallbacks
        sender = function_args.get("sender")
        message = function_args.get("message")

        if not sender:
            logger.error(f"Missing 'sender' in function_args: {function_args}")
            # Try to extract from trace_id or other sources
            if trace_id and ":" in str(trace_id):
                sender = str(trace_id)
                logger.warning(f"Using trace_id as sender fallback: {sender}")
            else:
                raise ValueError(
                    "Missing required 'sender' parameter in function arguments"
                )

        if not message:
            logger.error(f"Missing 'message' in function_args: {function_args}")
            raise ValueError(
                "Missing required 'message' parameter in function arguments"
            )

        logger.info(f"Processing player update request from {sender}")
        return await process_player_update_request(
            sender, message, client, user_sessions, trace_id
        )

    async def _handle_unclassified(
        self, function_args: Dict[str, Any], trace_id: str = None
    ):
        """Handle unclassified messages with helpful error responses."""
        sender = function_args["sender"]
        message = function_args["message"]
        message_type = function_args.get("message_type", "unclear")

        logger.info(f"Handling unclassified message from {sender}: {message_type}")

        # Create helpful error message
        error_message = "I couldn't understand your request. "

        error_message += "\n\n*Here's how to format your requests:*\n"
        error_message += "\n *Player Upload:* Include a Transfermarkt URL"
        error_message += "\n *Player Update:* Specify player name + details to change"
        error_message += "\n *Team Requests:* Include team, position, salary & requirements in one message"

        send_whatsapp_message(sender, error_message)

        # Trace the response if thread_id is available
        if trace_id:
            try:
                from app.monitoring.whatsapp_tracer import trace_response

                await trace_response(trace_id, sender, error_message, True)
            except Exception as e:
                logger.warning(f"Failed to trace unclassified response: {e}")

        return {"status": "unclassified", "message_type": message_type}

    async def _handle_unknown_handler(self, sender: str, message: str):
        """Handle case where requested handler doesn't exist."""
        logger.error(f"Unknown handler requested for message from {sender}")

        error_message = "I encountered an error processing your request. Please try again or contact support."
        send_whatsapp_message(sender, error_message)

        return {"status": "error", "error": "unknown_handler"}

    async def _handle_execution_error(
        self, sender: str, message: str, handler_name: str, error: str
    ):
        """Handle errors during handler execution."""
        logger.error(f"Handler {handler_name} failed for {sender}: {error}")

        error_message = "I encountered an error processing your request. Please try again in a moment."
        send_whatsapp_message(sender, error_message)

        return {"status": "error", "handler": handler_name, "error": error}

    def _update_performance_metrics(
        self, handler_name: str, duration: float, success: bool
    ):
        """Update performance metrics for a handler."""
        metrics = self.performance_metrics[handler_name]

        metrics["total_calls"] += 1
        metrics["total_duration"] += duration
        metrics["average_duration"] = metrics["total_duration"] / metrics["total_calls"]
        metrics["last_called"] = time.time()

        if not success:
            metrics["error_count"] += 1

    def get_handler_info(self, handler_name: str) -> Optional[Dict[str, Any]]:
        """Get information about a specific handler."""
        if handler_name not in self.handlers:
            return None

        return {
            "metadata": self.handler_metadata[handler_name],
            "performance": self.performance_metrics[handler_name],
            "available": True,
        }

    def get_all_handlers_info(self) -> Dict[str, Dict[str, Any]]:
        """Get information about all registered handlers."""
        return {
            handler_name: self.get_handler_info(handler_name)
            for handler_name in self.handlers.keys()
        }

    def get_performance_summary(self) -> Dict[str, Any]:
        """Get a summary of handler performance metrics."""
        total_calls = sum(
            metrics["total_calls"] for metrics in self.performance_metrics.values()
        )
        total_errors = sum(
            metrics["error_count"] for metrics in self.performance_metrics.values()
        )

        return {
            "total_calls": total_calls,
            "total_errors": total_errors,
            "error_rate": (total_errors / total_calls * 100) if total_calls > 0 else 0,
            "handlers": {
                name: {
                    "calls": metrics["total_calls"],
                    "avg_duration": metrics["average_duration"],
                    "errors": metrics["error_count"],
                }
                for name, metrics in self.performance_metrics.items()
            },
        }
