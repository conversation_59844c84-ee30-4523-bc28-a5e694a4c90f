import requests
import time
import logging
from typing import Dict, Any, Optional
from app.config import settings
from app.utils.whatsapp_handler import send_whatsapp_message

logger = logging.getLogger(__name__)


class WhatsAppBotHTTPClient:
    """
    HTTP client for WhatsApp bot backend API calls with security features:
    - Automatic API key header inclusion
    - Rate limiting handling
    - Error response handling (401, 429, 403)
    """
    
    def __init__(self):
        self.api_key = settings.WHATSAPP_BOT_API_KEY
        self.base_headers = {
            "Content-Type": "application/json",
            "X-API-Key": self.api_key
        }
        self.rate_limit_retry_delay = 60  # seconds to wait on 429 error
        
    def _get_headers(self, additional_headers: Optional[Dict[str, str]] = None) -> Dict[str, str]:
        """Get headers with API key and any additional headers."""
        headers = self.base_headers.copy()
        if additional_headers:
            headers.update(additional_headers)
        return headers
    
    def _handle_error_response(self, response: requests.Response, sender: str) -> None:
        """Handle specific error responses and send appropriate WhatsApp messages."""
        if response.status_code == 401:
            logger.error("API key authentication failed")
            send_whatsapp_message(
                sender, 
                "⚠️ Authentication error. Please contact support."
            )
        elif response.status_code == 403:
            logger.error(f"Phone number {sender} not authorized")
            send_whatsapp_message(
                sender, 
                "⚠️ Your phone number is not authorized for this service. Please contact support."
            )
        elif response.status_code == 429:
            logger.warning(f"Rate limit exceeded for {sender}")
            send_whatsapp_message(
                sender, 
                "⚠️ Too many requests. Please wait a moment before trying again."
            )
        else:
            logger.error(f"API request failed with status {response.status_code}: {response.text}")
            send_whatsapp_message(
                sender, 
                "⚠️ Service temporarily unavailable. Please try again later."
            )
    
    def post(self, url: str, json_data: Dict[str, Any], sender: str, 
             additional_headers: Optional[Dict[str, str]] = None, 
             retry_on_rate_limit: bool = True) -> Optional[requests.Response]:
        """
        Make a POST request with automatic error handling.
        
        Args:
            url: The API endpoint URL
            json_data: The JSON payload to send
            sender: The WhatsApp sender (for error messaging)
            additional_headers: Any additional headers to include
            retry_on_rate_limit: Whether to retry once on 429 error
            
        Returns:
            Response object if successful, None if error occurred
        """
        headers = self._get_headers(additional_headers)
        
        try:
            logger.info(f"Making POST request to {url}")
            response = requests.post(url, json=json_data, headers=headers, timeout=30)
            
            # Handle rate limiting with retry
            if response.status_code == 429 and retry_on_rate_limit:
                logger.warning(f"Rate limit hit, waiting {self.rate_limit_retry_delay} seconds before retry")
                time.sleep(self.rate_limit_retry_delay)
                
                # Retry once
                response = requests.post(url, json=json_data, headers=headers, timeout=30)
            
            # Check for success
            if response.status_code == 200 or response.status_code == 201:
                logger.info(f"API request successful: {response.status_code}")
                return response
            else:
                self._handle_error_response(response, sender)
                return None
                
        except requests.exceptions.Timeout:
            logger.error("API request timed out")
            send_whatsapp_message(sender, "⚠️ Request timed out. Please try again.")
            return None
        except requests.exceptions.ConnectionError:
            logger.error("Failed to connect to API")
            send_whatsapp_message(sender, "⚠️ Connection error. Please try again later.")
            return None
        except Exception as e:
            logger.error(f"Unexpected error during API request: {e}")
            send_whatsapp_message(sender, "⚠️ An unexpected error occurred. Please try again.")
            return None


# Create a singleton instance for use across the application
http_client = WhatsAppBotHTTPClient()


def create_player_record(phone_number: str, payload: Dict[str, Any]) -> Optional[requests.Response]:
    """
    Create a player record via the backend API.
    
    Args:
        phone_number: The WhatsApp phone number
        payload: The player data payload
        
    Returns:
        Response object if successful, None if error occurred
    """
    url = f"{settings.PLATFORM_URL}/player_records/whatsapp?phone_number={phone_number}"
    return http_client.post(url, payload, f"whatsapp:{phone_number}")


def create_team_requests_bulk(phone_number: str, payload: list) -> Optional[requests.Response]:
    """
    Create team requests in bulk via the backend API.
    
    Args:
        phone_number: The WhatsApp phone number
        payload: The team requests payload (list of requests)
        
    Returns:
        Response object if successful, None if error occurred
    """
    url = f"{settings.PLATFORM_URL}/team_requests/whatsapp/bulk?phone_number={phone_number}"
    return http_client.post(url, payload, f"whatsapp:{phone_number}")
